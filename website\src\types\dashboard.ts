/**
 * 用户仪表板相关类型定义
 */

// 虚拟货币类型
export interface VirtualCurrency {
  shells: number      // 贝壳数量
  diamonds: number    // 钻石数量
}

// 用户投资项目
export interface UserProject {
  id: number
  name: string
  investAmount: number        // 投资的贝壳数量
  startDate: string
  endDate: string
  status: string
  progress: number
  returns: number            // 收益的钻石数量
  expectedTotal: number      // 预期总收益钻石数量
  fundCode: string
  projectType: string
  riskLevel: string
  currentValue: number
  totalReturn: number
  monthlyReturn: number
}

// 资产概览（更新为贝壳钻石系统）
export interface AssetOverview {
  totalShells: number           // 总投资贝壳数
  consumedShells: number        // 已消耗贝壳数
  totalDiamonds: number         // 累计收益钻石数
  returnRate: number            // 收益率 = 钻石收益 ÷ 已消耗的贝壳数
  availableShells: number       // 可用贝壳数
  frozenShells: number          // 冻结贝壳数
  projectsCount: number         // 投资项目数
  monthlyDiamonds: number       // 本月钻石收益
}

// 收益趋势数据点（支持双Y轴）
export interface ReturnTrendPoint {
  month: string
  consumedShells: number    // 消耗的贝壳数（左Y轴）
  earnedDiamonds: number    // 收益的钻石数（右Y轴）
}

// 投资分布（短剧项目占比）
export interface InvestmentDistribution {
  projectName: string       // 短剧项目名称
  shellsAmount: number      // 投资的贝壳数量
  percentage: number        // 投资占比
  color: string
}

// 充值记录
export interface RechargeRecord {
  id: number
  transactionNo: string    // 交易流水号
  orderNo: string         // 订单号
  amount: number          // 充值贝壳数量
  balanceBefore: number   // 充值前余额
  balanceAfter: number    // 充值后余额
  description: string     // 交易描述
  paymentMethod: string   // 支付方式
  status: 'pending' | 'completed' | 'failed' | 'cancelled'  // 充值状态
  createdAt: string       // 充值时间
  updatedAt: string       // 更新时间
  completedAt?: string    // 完成时间
  failReason?: string     // 失败原因
}

// 提现记录
export interface WithdrawRecord {
  id: number
  amount: number          // 提现钻石数量
  bankAccount: string     // 银行账户
  status: string         // 提现状态
  createdAt: string      // 提现时间
  processedAt?: string   // 处理时间
}

// 风险分布
export interface RiskDistribution {
  level: string
  amount: number
  percentage: number
  color: string
}

// 交易记录
export interface Transaction {
  id: number
  type: string
  project: string
  amount: number
  date: string
  status: string
  description: string
}

// 通知消息
export interface Notification {
  id: number
  type: string
  title: string
  message: string
  date: string
  read: boolean
}

// 市场概览
export interface MarketOverview {
  totalMarketSize: string
  yearGrowthRate: string
  activeProjects: number
  totalInvestors: number
  averageReturn: string
  successRate: string
}

// 仪表板数据汇总
export interface DashboardData {
  userProjects: UserProject[]
  assetOverview: AssetOverview
  returnTrends: ReturnTrendPoint[]
  investmentDistribution: InvestmentDistribution[]
  riskDistribution: RiskDistribution[]
  recentTransactions: Transaction[]
  notifications: Notification[]
  marketOverview: MarketOverview
  rechargeRecords: RechargeRecord[]
  withdrawRecords: WithdrawRecord[]
}

// 充值请求参数
export interface RechargeRequest {
  amount: number          // 充值贝壳数量
  paymentMethod: string   // 支付方式
}

// 提现请求参数
export interface WithdrawRequest {
  amount: number          // 提现钻石数量
  bankAccount: string     // 银行账户
  password: string        // 交易密码
}
