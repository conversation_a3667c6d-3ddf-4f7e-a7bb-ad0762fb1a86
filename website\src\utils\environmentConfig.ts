// 环境配置类型定义
export interface Environment {
  name: string
  apiBaseUrl: string
  dbHost?: string
  dbPort?: number
  dbName?: string
  description?: string
}

// 环境配置映射
const environments: Record<string, Environment> = {
  development: {
    name: '开发环境',
    apiBaseUrl: 'http://localhost:3002',
    dbHost: 'localhost',
    dbPort: 3306,
    dbName: 'mengtu'
  },
  testing: {
    name: '测试环境',
    apiBaseUrl: 'http://test-server:3001', // 替换为您的测试服务器地址
    dbHost: 'test-server',
    dbPort: 3306,
    dbName: 'mengtu_test'
  },
  production: {
    name: '生产环境',
    apiBaseUrl: 'https://api.qinghee.com.cn/api', // 更新为新域名
    dbHost: '*************',
    dbPort: 3306,
    dbName: 'mengtu'
  }
}

// === 当前活动环境 ===
// 切换环境时，只需修改此常量
// 可选值: 'development', 'testing', 'production'
export const ACTIVE_ENVIRONMENT = 'development'

// 获取当前环境配置
export const getCurrentEnvironment = (): Environment => {
  // 优先使用import.meta.env中的环境变量（Vite提供）
  if (import.meta.env.VITE_API_BASE_URL) {
    const customEnv: Environment = {
      name: import.meta.env.VITE_APP_TITLE || '自定义环境',
      apiBaseUrl: import.meta.env.VITE_API_BASE_URL,
      description: import.meta.env.VITE_APP_DESCRIPTION || ''
    }
    return customEnv
  }
  
  // 如果没有环境变量，则使用预设的环境
  return environments[ACTIVE_ENVIRONMENT] || environments.development
}

// 获取当前环境的API基础URL (不包含/api路径)
export const getApiBaseUrl = (): string => {
  // 优先使用环境变量
  const envApiBaseUrl = import.meta.env.VITE_API_BASE_URL
  if (envApiBaseUrl) {
    // 如果环境变量中的URL已经包含/api，则直接返回
    return envApiBaseUrl.endsWith('/api') 
      ? envApiBaseUrl 
      : envApiBaseUrl
  }
  
  // 否则使用预设配置
  return getCurrentEnvironment().apiBaseUrl
}

// 获取完整的API URL (确保包含/api路径)
export const getCompleteApiUrl = (): string => {
  // 优先使用环境变量
  const envApiBaseUrl = import.meta.env.VITE_API_BASE_URL
  if (envApiBaseUrl) {
    return envApiBaseUrl
  }

  // 否则使用预设配置并确保包含/api
  const baseUrl = getCurrentEnvironment().apiBaseUrl
  return `${baseUrl}/api`
}

// 导出环境配置
export { environments }
