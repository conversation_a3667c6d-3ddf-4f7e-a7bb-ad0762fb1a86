<template>
  <div class="bg-white rounded-lg shadow-sm p-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-4 text-center">快捷功能</h3>
    
    <!-- 9宫格布局 -->
    <div class="grid grid-cols-3 gap-4">
      <!-- 充值记录 -->
      <div
        @click="goToRechargeRecords"
        class="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-green-300 hover:bg-green-50 transition-all cursor-pointer group"
      >
        <div class="w-12 h-12 mb-2 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
          <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        </div>
        <span class="text-sm font-medium text-gray-700 group-hover:text-green-700">充值记录</span>
        <div v-if="rechargeSummary" class="text-xs text-gray-500 mt-1">
          {{ rechargeSummary.completedCount }}笔
        </div>
      </div>

      <!-- 投资记录 -->
      <div 
        @click="goToInvestmentRecords"
        class="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all cursor-pointer group"
      >
        <div class="w-12 h-12 mb-2 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
          <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        </div>
        <span class="text-sm font-medium text-gray-700 group-hover:text-blue-700">投资记录</span>
      </div>

      <!-- 收益记录 -->
      <div 
        @click="goToReturnsRecords"
        class="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-yellow-300 hover:bg-yellow-50 transition-all cursor-pointer group"
      >
        <div class="w-12 h-12 mb-2 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
          <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
          </svg>
        </div>
        <span class="text-sm font-medium text-gray-700 group-hover:text-yellow-700">收益记录</span>
      </div>

      <!-- 银行卡管理 -->
      <div 
        @click="goToBankCards"
        class="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all cursor-pointer group"
      >
        <div class="w-12 h-12 mb-2 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
          <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
          </svg>
        </div>
        <span class="text-sm font-medium text-gray-700 group-hover:text-purple-700">银行卡</span>
      </div>

      <!-- 个人资料 -->
      <div 
        @click="goToProfile"
        class="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-indigo-300 hover:bg-indigo-50 transition-all cursor-pointer group"
      >
        <div class="w-12 h-12 mb-2 bg-gradient-to-br from-indigo-400 to-indigo-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
          <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        </div>
        <span class="text-sm font-medium text-gray-700 group-hover:text-indigo-700">个人资料</span>
      </div>

      <!-- 安全设置 -->
      <div 
        @click="goToSecurity"
        class="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-red-300 hover:bg-red-50 transition-all cursor-pointer group"
      >
        <div class="w-12 h-12 mb-2 bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
          <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        </div>
        <span class="text-sm font-medium text-gray-700 group-hover:text-red-700">安全设置</span>
      </div>

      <!-- 帮助中心 -->
      <div 
        @click="goToHelp"
        class="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-gray-400 hover:bg-gray-50 transition-all cursor-pointer group"
      >
        <div class="w-12 h-12 mb-2 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
          <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <span class="text-sm font-medium text-gray-700 group-hover:text-gray-700">帮助中心</span>
      </div>

      <!-- 消息通知 -->
      <div 
        @click="goToNotifications"
        class="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-orange-300 hover:bg-orange-50 transition-all cursor-pointer group"
      >
        <div class="w-12 h-12 mb-2 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
          <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
          </svg>
        </div>
        <span class="text-sm font-medium text-gray-700 group-hover:text-orange-700">消息通知</span>
      </div>

      <!-- 设置 -->
      <div 
        @click="goToSettings"
        class="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-teal-300 hover:bg-teal-50 transition-all cursor-pointer group"
      >
        <div class="w-12 h-12 mb-2 bg-gradient-to-br from-teal-400 to-teal-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
          <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </div>
        <span class="text-sm font-medium text-gray-700 group-hover:text-teal-700">设置</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 充值记录统计数据
const rechargeSummary = ref(null)

// 获取充值记录统计信息
const fetchRechargeSummary = async () => {
  try {
    const response = await fetch('http://localhost:3001/api/users/wallet/recharge/summary')
    const data = await response.json()

    if (data.success) {
      rechargeSummary.value = data.data.summary
    }
  } catch (error) {
    console.error('获取充值统计信息失败:', error)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchRechargeSummary()
})

// 导航函数
const goToRechargeRecords = () => {
  router.push('/recharge-records')
}

const goToInvestmentRecords = () => {
  router.push('/investment-records')
}

const goToReturnsRecords = () => {
  console.log('跳转到收益记录')
  // TODO: 实现收益记录页面
}

const goToBankCards = () => {
  console.log('跳转到银行卡管理')
  // TODO: 实现银行卡管理页面
}

const goToProfile = () => {
  console.log('跳转到个人资料')
  // TODO: 实现个人资料页面
}

const goToSecurity = () => {
  console.log('跳转到安全设置')
  // TODO: 实现安全设置页面
}

const goToHelp = () => {
  console.log('跳转到帮助中心')
  // TODO: 实现帮助中心页面
}

const goToNotifications = () => {
  console.log('跳转到消息通知')
  // TODO: 实现消息通知页面
}

const goToSettings = () => {
  console.log('跳转到设置')
  // TODO: 实现设置页面
}
</script>
