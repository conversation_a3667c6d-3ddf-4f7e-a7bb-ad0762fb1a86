/**
 * 钱包相关API接口 - 贝壳充值和钻石提现
 */

import axios, { AxiosResponse } from 'axios'
import { getCompleteApiUrl } from '../utils/environmentConfig'
import type {
  ApiResponse,
  RechargeRequest,
  WithdrawRequest,
  RechargeRecord,
  WithdrawRecord
} from '../types'

// API基础URL
const BASE_URL = getCompleteApiUrl()

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加令牌到请求头
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

/**
 * 贝壳充值
 * @param rechargeData 充值数据
 */
export const rechargeShells = async (rechargeData: RechargeRequest): Promise<ApiResponse<{
  orderId: string
  paymentUrl?: string
  qrCode?: string
}>> => {
  try {
    const response = await api.post('/users/wallet/recharge', rechargeData)
    return response.data
  } catch (error) {
    throw error
  }
}

/**
 * 钻石提现
 * @param withdrawData 提现数据
 */
export const withdrawDiamonds = async (withdrawData: WithdrawRequest): Promise<ApiResponse<{
  withdrawId: string
  estimatedArrival: string
}>> => {
  try {
    const response = await api.post('/users/wallet/withdraw', withdrawData)
    return response.data
  } catch (error) {
    throw error
  }
}

/**
 * 获取支付方式列表
 */
export const getPaymentMethods = async (): Promise<ApiResponse<{
  id: string
  name: string
  icon: string
  enabled: boolean
  minAmount: number
  maxAmount: number
}[]>> => {
  try {
    const response = await api.get('/users/wallet/payment-methods')
    return response.data
  } catch (error) {
    throw error
  }
}

/**
 * 获取银行卡列表
 */
export const getBankCards = async (): Promise<ApiResponse<{
  id: string
  bankName: string
  cardNumber: string
  cardHolder: string
  isDefault: boolean
}[]>> => {
  try {
    const response = await api.get('/users/wallet/bank-cards')
    return response.data
  } catch (error) {
    throw error
  }
}

/**
 * 添加银行卡
 */
export const addBankCard = async (cardData: {
  bankName: string
  cardNumber: string
  cardHolder: string
  idCard: string
  phone: string
}): Promise<ApiResponse<void>> => {
  try {
    const response = await api.post('/users/wallet/bank-cards', cardData)
    return response.data
  } catch (error) {
    throw error
  }
}

/**
 * 查询充值订单状态
 */
export const getRechargeOrderStatus = async (orderId: string): Promise<ApiResponse<{
  status: 'pending' | 'success' | 'failed' | 'cancelled'
  amount: number
  createdAt: string
  paidAt?: string
}>> => {
  try {
    const response = await api.get(`/users/wallet/recharge/${orderId}`)
    return response.data
  } catch (error) {
    throw error
  }
}

/**
 * 获取充值记录列表
 */
export const getRechargeRecords = async (page: number = 1, pageSize: number = 10): Promise<ApiResponse<{
  records: RechargeRecord[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}>> => {
  try {
    const response = await api.get('/users/wallet/recharge/records', {
      params: { page, pageSize }
    })
    return response.data
  } catch (error) {
    throw error
  }
}

/**
 * 获取充值记录统计信息
 */
export const getRechargeRecordsSummary = async (): Promise<ApiResponse<{
  totalCount: number
  totalAmount: number
  completedCount: number
  pendingCount: number
  failedCount: number
  lastRechargeDate?: string
}>> => {
  try {
    const response = await api.get('/users/wallet/recharge/summary')
    return response.data
  } catch (error) {
    throw error
  }
}

/**
 * 查询提现申请状态
 */
export const getWithdrawStatus = async (withdrawId: string): Promise<ApiResponse<{
  status: 'pending' | 'processing' | 'success' | 'failed' | 'cancelled'
  amount: number
  createdAt: string
  processedAt?: string
  reason?: string
}>> => {
  try {
    const response = await api.get(`/users/wallet/withdraw/${withdrawId}`)
    return response.data
  } catch (error) {
    throw error
  }
}

/**
 * 取消充值订单
 */
export const cancelRechargeOrder = async (orderId: string): Promise<ApiResponse<void>> => {
  try {
    const response = await api.post(`/users/wallet/recharge/${orderId}/cancel`)
    return response.data
  } catch (error) {
    throw error
  }
}

/**
 * 取消提现申请
 */
export const cancelWithdrawRequest = async (withdrawId: string): Promise<ApiResponse<void>> => {
  try {
    const response = await api.post(`/users/wallet/withdraw/${withdrawId}/cancel`)
    return response.data
  } catch (error) {
    throw error
  }
}

// 导出所有API方法
export default {
  rechargeShells,
  withdrawDiamonds,
  getPaymentMethods,
  getBankCards,
  addBankCard,
  getRechargeOrderStatus,
  getRechargeRecords,
  getRechargeRecordsSummary,
  getWithdrawStatus,
  cancelRechargeOrder,
  cancelWithdrawRequest
}
