/**
 * 获取用户充值记录统计信息接口
 * GET /api/users/wallet/recharge/summary
 */

import { query } from '~/utils/database'

export default defineEventHandler(async (event) => {
  try {
    console.log('Recharge summary API called');
    // 暂时跳过认证，直接使用用户ID 6进行测试
    const userId = 6;

    // 查询充值记录统计信息
    const summaryQuery = `
      SELECT 
        COUNT(*) as total_count,
        COALESCE(SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END), 0) as total_amount,
        COALESCE(SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END), 0) as completed_count,
        COALESCE(SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END), 0) as pending_count,
        COALESCE(SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END), 0) as failed_count,
        MAX(CASE WHEN status = 'completed' THEN created_at ELSE NULL END) as last_recharge_date
      FROM user_asset_transactions
      WHERE user_id = ? 
        AND transaction_type = 'shells_in' 
        AND related_type = 'recharge'
    `;

    const summaryResult = await query(summaryQuery, [userId]);
    const summary = summaryResult[0];

    // 查询最近的充值记录
    const recentQuery = `
      SELECT 
        amount,
        status,
        DATE_FORMAT(created_at, '%Y-%m-%d %H:%i') as created_at
      FROM user_asset_transactions
      WHERE user_id = ? 
        AND transaction_type = 'shells_in' 
        AND related_type = 'recharge'
      ORDER BY created_at DESC
      LIMIT 3
    `;

    const recentRecords = await query(recentQuery, [userId]);

    return {
      success: true,
      data: {
        summary: {
          totalCount: parseInt(summary.total_count) || 0,
          totalAmount: parseFloat(summary.total_amount) || 0,
          completedCount: parseInt(summary.completed_count) || 0,
          pendingCount: parseInt(summary.pending_count) || 0,
          failedCount: parseInt(summary.failed_count) || 0,
          lastRechargeDate: summary.last_recharge_date
        },
        recentRecords: recentRecords.map(record => ({
          amount: parseFloat(record.amount),
          status: record.status,
          createdAt: record.created_at
        }))
      }
    };

  } catch (error: any) {
    console.error('获取充值统计信息失败:', error);
    return {
      success: false,
      message: '获取充值统计信息失败',
      error: error.message
    };
  }
});
